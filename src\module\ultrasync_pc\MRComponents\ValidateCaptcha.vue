<template>
    <div>
      <div :id="`${this.name}-captcha-button`" v-show="false"></div>
      <div :id="`${this.name}-captcha-element`"></div>
    </div>
  </template>

<script>
import CEvent from '@/common/CEvent'
import service from '../service/service'
import Tool from '@/common/tool'
import {getLanguage} from '@/common/i18n'
export default {
    props: {
        name: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            captcha: null,
            captchaButton: null,
            event: null,
            captchaVerifyParam: '',
            openValidating:false,
            validatePromise:{
                promise:null,
                resolve:null,
                reject:null
            },
            afsCode:'',
            isSuccessValid:false,
            sdkInitialized: false, // 标记SDK是否已初始化
            needReinitAfterSuccess: false, // 标记验证成功后是否需要重新初始化
        }
    },
    mounted() {
        this.event = new CEvent()
        this.initSDK() // 在mounted中初始化SDK
    },
    beforeUnmount() {
        this.destroyCaptcha()
    },
    methods: {
        // 初始化SDK，在mounted中调用
        initSDK() {
            return new Promise((resolve, reject) => {
                this.event.on('getInstance', () => {
                    this.sdkInitialized = true
                    resolve(true)
                })
                try {
                    this.captchaButton = document.getElementById(`${this.name}-captcha-button`)
                    const currentLanguage = getLanguage()
                    const languageMap = {
                        CN:'cn',
                        EN:'en',
                        ES:'es',
                        PTBR:'pt',
                        RU:'ru'
                    }
                    const useLang = languageMap[currentLanguage]||'en'
                    let sceneInfo = {
                        SceneId:'s4yebkic',
                        prefix: '153y4u'
                    }
                    if(Tool.isDev() || Tool.isBeta()|| Tool.isCustomEnv()){
                        sceneInfo = {
                            SceneId:'1e4xl9lr',//1bqq236l 1e4xl9lr
                            prefix: '153y4u'
                        }
                    }
                    window.initAliyunCaptcha({
                        SceneId: sceneInfo.SceneId, // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
                        prefix: sceneInfo.prefix, // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
                        mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
                        element: `#${this.name}-captcha-element`, // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
                        button: `#${this.name}-captcha-button`, // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
                        success: this.captchaVerifySuccess, // 验证码验证通过回调函数
                        fail: this.captchaVerifyFail, // 验证码验证不通过回调函数
                        getInstance: this.getInstance, // 绑定验证码实例函数，无需修改
                        slideStyle: {
                            width: 360,
                            height: 40
                        }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
                        language: useLang, // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
                        onClose: this.handleCaptchaClose,
                        onError: this.handleCaptchaError
                    })
                } catch (error) {
                    reject(error)
                }
            })
        },
        // 等待实例初始化完成并触发验证码
        async initCaptcha() {
            // 如果验证成功后需要重新初始化，先销毁当前实例
            if (this.needReinitAfterSuccess && this.isSuccessValid) {
                await this.reinitializeSDK()
            }

            // 如果SDK还没有初始化完成，等待初始化
            if (!this.sdkInitialized || !this.captcha) {
                await this.waitForInstance()
            }
            // 模拟触发button
            if (this.captchaButton) {
                this.captchaButton.click()
            }
        },
        // 重新初始化SDK
        async reinitializeSDK() {
            // 销毁当前实例
            if (this.captcha) {
                this.destroyCaptcha()
            }

            // 重置状态
            this.sdkInitialized = false
            this.isSuccessValid = false
            this.needReinitAfterSuccess = false

            // 重新初始化
            await this.initSDK()
        },
        // 等待实例获取完成
        waitForInstance() {
            return new Promise((resolve) => {
                if (this.sdkInitialized && this.captcha) {
                    resolve()
                    return
                }

                const checkInstance = () => {
                    if (this.sdkInitialized && this.captcha) {
                        resolve()
                    } else {
                        setTimeout(checkInstance, 100) // 每100ms检查一次
                    }
                }
                checkInstance()
            })
        },
        handleCaptchaClose(){
            this.openValidating = false
            this.validatePromise.reject('校验被关闭了')
        },
        handleCaptchaError(){
            this.openValidating = false
            this.validatePromise.reject('校验失败')
        },
        destroyCaptcha() {
            if (this.captcha && typeof this.captcha.destroyCaptcha === 'function') {
                this.captcha.destroyCaptcha()
            }
            this.captcha = null
            this.captchaButton = null
            this.captchaVerifyParam = null
            this.isSuccessValid = false
        },
        getInstance(instance) {
            this.captcha = instance
            this.event.emit('getInstance', true)
        },
        async captchaVerifySuccess(captchaVerifyParam) {
            console.error('captchaVerifySuccess',captchaVerifyParam)
            this.needReinitAfterSuccess = true // 标记验证成功后需要重新初始化
            try {
                const {isSuccess,afsCode} = await this.ServiceCaptchaVerify(captchaVerifyParam)
                if(isSuccess){
                    this.captchaVerifyParam = captchaVerifyParam
                    this.afsCode = afsCode
                    this.isSuccessValid = true

                    console.error('captchaVerifySuccess',afsCode)
                    this.event.emit('onBizResultCallback', {
                        captchaResult: true,
                        afsCode:afsCode
                    })
                }else{
                    this.event.emit('onBizResultCallback', {
                        captchaResult: false
                    })
                }

            } catch (error) {
                console.error(error)
                this.event.emit('onBizResultCallback', {
                    captchaResult: false
                })
            }

        },
        //发送网络请求
        ServiceCaptchaVerify(captchaVerifyParam){
            return new Promise((resolve,reject)=>{
                service.loginByTraceless({
                    data:captchaVerifyParam
                }).then(res => {
                    console.log('loginByTraceless result',res)
                    if(res.data.error_code === 0){
                        resolve(res.data.data)
                    }else{
                        reject(false)
                    }
                }).catch(error =>{
                    console.error(error)
                    reject(false)
                })
            })

        },
        // 验证码验证不通过回调函数
        captchaVerifyFail(result) {
            console.error('captchaVerifyFail', result)

        },
        // 弹出验证框，popupId为组件id，用于支持验证通过后继续触发业务逻辑
        popup(popupId) {
            this.popupId = popupId
            this.captchaButton.click()
        },
        validateCaptcha() {
            console.error(this.openValidating ,'openValidating')
            if(this.openValidating){
                return
            }
            this.openValidating = true
            if(this.validatePromise.promise){
                this.validatePromise.reject('Previous validation was interrupted')
            }
            this.validatePromise.promise = new Promise(async(resolve, reject) => {
                this.validatePromise.resolve = resolve
                this.validatePromise.reject = reject
                this.event.on('onBizResultCallback', (res) => {
                    if (res.captchaResult) {
                        resolve(res.afsCode)
                    } else {
                        reject(false)
                    }
                })
                try {
                    await this.initCaptcha() // 使用新的initCaptcha方法
                } catch (error) {
                    console.error('initCaptcha',error)
                    this.openValidating = false
                    reject(false)
                }
                this.openValidating = false
            })
            return this.validatePromise.promise
        }
    }
}
</script>
<style lang="scss">
#aliyunCaptcha-window-popup{
    top:50%!important;
    #aliyunCaptcha-title{
        font-size:16px;
    }
}
</style>
