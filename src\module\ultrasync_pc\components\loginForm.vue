<template>
    <div class="login_form_container">
        <div class="logo_box">
            <i class="icon iconfont iconfanhui" @click="backToLogin" v-if="isShowBackBtn"></i>
            <img src="static/resource_pc/images/logo.png">
            <p>{{$t('welcome_tip')}}</p>
        </div>
        <login-qr-code v-if="loginType===4"></login-qr-code>
        <template v-else>
            <img src="static/resource_pc/images/qrcode.png" class="login_qrcode"  @click="changeLoginType(4)" v-if="supportLoginType.includes(4)">
            <div v-if="loginType===3" class="account_login" @keyup.enter="loginByTracelessValid">
                <el-form :model="login_form" label-width="0px" ref="login_form" id="login_form">
                    <div>
                        <el-form-item label="" prop="name">
                            <el-input v-model="login_form.name" :placeholder="isInternalNetworkEnv?$t('register_account'):$t('register_account_or_mobile')" autocomplete="new-password"></el-input>
                        </el-form-item>
                        <el-form-item label="" prop="pwd">
                            <div class="password-input-wrapper">
                                <el-input
                                    :type="isShowPassword ? 'text' : 'password'"
                                    v-model="login_form.pwd"
                                    :placeholder="$t('register_password')"
                                    autocomplete="new-password"
                                >
                                </el-input>
                                <i
                                    :class="isShowPassword ? 'iconos-icon-view' : 'iconos-icon-view-close'"
                                    class="password-toggle-icon-fixed iconfont"
                                    @click="togglePasswordVisibility"
                                ></i>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
                <span @click="loginByTracelessValid" class="login_btn common_btn" v-loading="isValidating||logging||isAutoLogin">{{$t('login_title')}}</span>
                <div class="setting clearfix">
                    <img src="static/resource_pc/images/mobile.png" class="fl" @click="changeLoginType(1)" v-if="!isInternalNetworkEnv">
                    <img src="static/resource_pc/images/email.png" class="fl" @click="changeLoginType(2)" v-if="!isInternalNetworkEnv">
                    <span class="go_to_btn fr" @click="showForgetDialog" v-if="!isInternalNetworkEnv">{{$t('to_forget_password')}}?</span>
                    <span class="go_to_btn fr" @click="isShowRegister = true" v-if="isInternalNetworkEnv">{{$t('to_register')}}</span>
                </div>
            </div>
            <div v-else-if="loginType===1" class="mobile_login" @keyup.enter="loginOrRegister">
                <p class="register_tip">{{$t('login_or_register_mobile')}}</p>
                <MobileInternational :type="2" :mobile.sync="mobile_number" :nationalCode.sync="international_code" ref='mobile_international'></MobileInternational>
                <div class="verify_item">
                    <el-input v-model="verifyCode" :placeholder="$t('sms_verification_code')" maxlength="6"></el-input>
                    <span v-show="counter==0" class="code_btn" @click="loginByTracelessValid" v-loading="isValidating||requesting">{{$t('forget_password_getcode')}}</span>
                    <span v-show="counter!=0" class="code_btn">{{counter}}s</span>
                </div>
                <span @click="loginOrRegister" class="login_btn common_btn" v-loading="logging||isAutoLogin">{{$t('login_title')}}</span>
                <div class="setting clearfix">
                    <img src="static/resource_pc/images/user.png" class="fl" @click="changeLoginType(3)">
                    <img src="static/resource_pc/images/email.png" class="fl" @click="changeLoginType(2)">
                </div>

            </div>
            <div v-else-if="loginType===2" class="email_login" @keyup.enter="loginOrRegister">
                <p class="register_tip">{{$t('login_or_register_email')}}</p>
                <div  class="verify_item">
                    <el-input v-model="email" :placeholder="$t('register_email')"></el-input>
                </div>
                <div class="verify_item">
                    <el-input v-model="verifyCode" :placeholder="$t('email_verification_code')" maxlength="6"></el-input>
                    <span v-show="counter==0" class="code_btn" @click="loginByTracelessValid" v-loading="isValidating||requesting">{{$t('forget_password_get_email_code')}}</span>
                    <span v-show="counter!=0" class="code_btn">{{counter}}s</span>
                </div>
                <span @click="loginOrRegister" class="login_btn common_btn" v-loading="logging||isAutoLogin">{{$t('login_title')}}</span>
                <div class="setting clearfix">
                    <img src="static/resource_pc/images/user.png" class="fl" @click="changeLoginType(3)">
                    <img src="static/resource_pc/images/mobile.png" class="fl" @click="changeLoginType(1)">
                </div>
            </div>
            <div class="login-form-checkbox">
                <el-checkbox class="agree_privacy_protocol" v-model="isAgreePrivacyPolicy" @click="isAgreePrivacyPolicy=!isAgreePrivacyPolicy">
                    <span @click.stop.prevent="toProtocol" class="privacy-protocol-link">《{{$t('ultrasync_privacy_protocol')}}》</span>
                </el-checkbox>
                <el-checkbox class="remember" v-model="remember" @click="remember=!remember">{{$t('auto_login')}}</el-checkbox>
            </div>
            <forget-password-form v-if="isShowForgetPassword"></forget-password-form>
            <RegisterByAccount v-if="isShowRegister"></RegisterByAccount>
        </template>
        <two-factor-authentication
        :isShowVerify.sync="isShowVerify"
        :cellphone="cellphone"
        :email="email"
        :token="token"
        :isLoading="logging"
        :verifyCallback="loginByVerify"
        :bindingCallback="loginByBinding"
        :customClass="'login_form'"
        ref="twoFactorAuthentication"
        ></two-factor-authentication>
        <ValidateCaptcha name="LoginForm" ref='validateCaptcha'></ValidateCaptcha>
    </div>


</template>

<script>
import base from '../lib/base'
import service from '../service/service'
import Tool from '@/common/tool.js'
import CWorkstationCommunicationMng from '@/common/CommunicationMng/index'
import {parseImageListToLocal,formatString,handleAfterLogin,goPrivacyPolicy} from '../lib/common_base'
import { getLanguage } from "@/common/i18n"
import twoFactorAuthentication from './twoFactorAuthentication.vue'
import MobileInternational from './mobileInternational.vue'
import LoginQrCode from '../components/loginQrCode.vue'
import ForgetPasswordForm from '../components/forgetPasswordForm.vue'
import ValidateCaptcha from '../MRComponents/ValidateCaptcha.vue'
import RegisterByAccount from '../components/registerByAccount.vue'
export default {
    mixins: [base],
    name: 'LoginForm',
    components: {
        twoFactorAuthentication,
        MobileInternational,
        LoginQrCode,
        ForgetPasswordForm,
        ValidateCaptcha,
        RegisterByAccount
    },
    data(){
        return {
            login_form:{
                name:'',
                pwd:'',
                device_id:'',
                token:'',
                mobile_phone:"",
                sms_verification_code:'',
                sms_verification_code_index:'',

                verification_code:"",
                verification_code_uuid:"",
            },
            remember:false,
            logging:false,
            device_id:'',
            deviceVerifyTimer:null,
            errorPwd:'',//用于保存错误的原密码
            isShowVerify:false,
            token:'',
            mobile_number:'',
            cellphone:'',
            email:'',
            loginType:3,//1手机登录，2邮箱登录,3账号登录,4扫码登录
            international_code:'',
            verifyCode:'',
            countTimer:null,
            counter:0,
            submitType:1,//1登录，2注册
            isShowForgetPassword:false,
            isAutoLogin:false,
            requesting:false, //发送获取验证码的请求中
            isShowRegister:false,
            isAgreePrivacyPolicy:false,//是否同意隐私政策
            isValidating:false,
            isShowPassword: false // 控制密码显示状态
        }
    },
    computed:{
        serverInfo() {
            return this.systemConfig.serverInfo
        },
        isShowSMSIdentify() {
            return this.serverInfo.enable_sms_identification;
        },
        deviceInfo(){
            return this.$store.state.device
        },
        supportLoginType(){
            let supportType = [1,2,3,4]
            if(this.isInternalNetworkEnv){
                supportType = [3]
            }
            return supportType
        },
        isInternalNetworkEnv(){
            return this.systemConfig.serverInfo.network_environment
        },
        isShowBackBtn(){
            return this.isShowForgetPassword||this.isShowRegister
        }
    },
    destroyed(){
        this.$root.eventBus.$off('NotifyGetDeviceID');
        if(this.deviceVerifyTimer){
            clearInterval(this.deviceVerifyTimer);
            this.deviceVerifyTimer = null;
        }
    },
    mounted(){
        this.$nextTick(()=>{
            let account=window.localStorage.getItem('account')||''
            let pwd=window.localStorage.getItem('password')
            this.login_form.name=account;
            if (pwd) {
                this.remember=true;
                this.login_form.pwd=pwd
            }
            this.login_form.extend_info = "";
            this.$root.eventBus.$off('notifyStartupOption').$on('notifyStartupOption', (data)=> {
                console.error('notifyStartupOption')
                window.g_extend_info = data;
                if (data) {
                    //第三方登录
                    window.localStorage.setItem('password','');
                    this.login_form.pwd="";
                    this.login_form.extend_info = data;
                    this.doLoginSubmit();
                }else{
                    setTimeout(()=>{
                        //延迟自动登录，防止闪烁
                        this.autoLogin();
                    },500)
                }
            });
            this.$root.eventBus.$off('NotifyGetDeviceID').$on('NotifyGetDeviceID',(param)=>{
                console.log("################### NotifyGetDeviceID login.vue ###################");
                this.device_id = param.device_id;
                if("GetDeviceID_only" != param.append_info){
                    CWorkstationCommunicationMng.QueryStartupOption();
                }
            })
            this.$root.eventBus.$off('changeLoginType').$on('changeLoginType',this.changeLoginType)
            this.$root.eventBus.$off('loginWithToken').$on('loginWithToken',this.loginWithToken)
            if(Tool.ifDeviceClientType(window.clientType)){ //设备认证登录 //temp test todo
                this.pollingDeviceIdentify();
            }
        })
    },
    methods:{
        doLoginSubmit(){
            this.logging=true;
            var language=getLanguage();
            let option = {
                type:this.systemConfig.clientType,
                response_data_type:1,
                language:language,
                device_id:this.device_id,
                token:this.login_form.token,
                extend_info: this.login_form.extend_info,
                verification_code: this.login_form.verification_code,
                verification_code_uuid: this.login_form.verification_code_uuid,
            };
            option.name = this.login_form.name;
            option.pwd = this.login_form.pwd;
            service.login({
                action_form:"login_action",
                user:option
            }).then((res)=>{
                this.logging=false;
                if(res.data&&res.data.uid){
                    res.data.fromLogin=true
                    parseImageListToLocal([res.data],'avatar')
                    this.setDefaultImg([res.data])
                    this.$store.commit('user/updateUser', res.data);
                    window.vm.$store.commit('dynamicGlobalParams/updateToken',res.data.new_token)
                    if (this.remember) {
                        if (res.data.login_name && res.data.crypt_password) {
                            this.login_form.name = res.data.login_name;
                            this.login_form.pwd = res.data.crypt_password;
                        }

                        window.localStorage.setItem('account',this.login_form.name)
                        window.localStorage.setItem('password',this.login_form.pwd)
                    }
                    this.$router.replace("/main/dashboard   ");
                    if (res.data.probationary_expiry) {
                        let msg=this.$t('nls_probationary_expiry_tip').replace('{1}',res.data.probationary_expiry)
                        setTimeout(()=>{
                            this.$MessageBox.alert(msg)
                        },1000)
                    }

                }else{
                    this.resetStartupOption();
                    let msg = this.$t('unknown_error');
                    if (res.data && res.data.msg) {
                        msg = res.data.msg;
                    }
                    if (res.data
                        && ("login_fail_account_or_password_incorrect" == res.data.error_code || "login_fail_mobile_or_sms_incorrect" == res.data.error_code)
                        && res.data.error_param
                        && res.data.error_param.times >= res.data.error_param.tip_count
                    ) {
                        msg = formatString(this.$t('login_fail_with_lock_tip'), {
                            1:res.data.error_param.check_period,
                            2:res.data.error_param.times,
                            3:res.data.error_param.max_count,
                            4:res.data.error_param.lock_time
                        })
                    }
                    this.login_form.pwd=this.errorPwd
                    this.$message.error(msg)
                }
            }).catch((e)=>{
                if (e.message=="Network Error") {
                    this.$message.error(this.$t('network_error_tip'));
                }else{
                    this.$message.error(e.message||e);
                }
                this.logging=false;
            })
        },
        resetStartupOption() {
            window.CWorkstationCommunicationMng.ClearStartupOption();
            window.g_extend_info = null;
        },
        pollingDeviceIdentify(){
            //轮询设备认证
            console.log("pollingDeviceIdentify");
            var that = this;

            that.deviceVerifyTimer = setInterval(function(){
                if('' == that.device_id){
                    if(window.CWorkstationCommunicationMng){
                        //刚开启app，可能CWorkstationCommunicationMng还未初始化完
                        window.CWorkstationCommunicationMng.GetDeviceID({append_info:"GetDeviceID_only"});
                    }
                }else{
                    console.log("send device_identify to server");

                    var params = {device_id: that.device_id, device_type: window.clientType};
                    console.log(params);

                    service.device_identify(params).then((res) => {
                        console.log("[info] device identify send result", res.data);
                        var data = res.data;
                        if(data.event_type == "login"){
                            that.login_form.name = "";
                            that.login_form.password == "";
                            //$("#validateTips").empty();

                            //$("#loginForm").find("#device_id").val(data.device_id);
                            //$("#loginForm").find("#login_token").val(data.token);
                            //localStorage.setItem('local_store_device_token', JSON.stringify({device_id:data.device_id, token: data.token}));
                            //$('#loginForm').submit();

                            that.login_form.device_id = data.device_id;
                            that.login_form.token = data.token;
                            window.localStorage.setItem('local_store_device_token', JSON.stringify({device_id:data.device_id, token: data.token}));
                            that.doLoginSubmit();
                        }
                    }).catch((error) => {
                        console.log("[error] device_identify send failed! ");
                    });
                }
            }, 3000)
        },
        loginOrRegister(afsCode){
            if (this.submitType===1) {
                //登录
                if (this.loginType===1||this.loginType===2) {
                    this.loginByCode()
                } else if (this.loginType===3) {
                    this.loginV2(afsCode);
                }
            } else if (this.submitType===2) {
                //注册
                if (this.loginType===1||this.loginType===2) {
                    this.register()
                }
            }
        },
        loginV2(afsCode){
            if (!this.validateLoginForm()) {
                return ;
            }
            this.logging = true
            this.errorPwd=this.login_form.pwd
            service.encryptPassword({
                pwd:this.login_form.pwd
            }).then((res)=>{
                if (res.data.error_code===0) {
                    const tokenParams={
                        loginName:this.login_form.name,
                        password:res.data.data.encryptStr,
                        language:getLanguage(),
                        clientType:this.systemConfig.clientType,
                        deviceId:this.deviceInfo.device_id,
                        afsCode:afsCode
                    }
                    service.getLoginToken(tokenParams).then((res)=>{
                        this.logging=false
                        if (res.data.error_code===0) {
                            const result=res.data.data;
                            // result.need_code_login=true;
                            // result.mobile_number='';
                            // result.email='';
                            this.twoFactorLogin(result);
                            // if (result.need_code_login) {
                            //     this.verifyWithToken(res.data.data)
                            // }else{
                            //     this.loginWithToken(res.data.data.token);
                            // }
                        }else{
                            // this.$message.error(this.$t(res.data.key)||this.$t('unknown_error'))
                        }

                    }).catch((e)=>{
                        this.$message.error(e);
                        this.logging=false
                    })
                }else{
                    this.logging=false
                }
            }).catch((e)=>{
                this.$message.error(e);
                this.logging=false
            })
        },
        twoFactorLogin(result){
            const needBind=result.cellphone==''&&result.email==''&&this.isShowSMSIdentify;
            if (result.userOutOfTrail) {
                //账号超过试用期，先去输推荐码
                const isRemember=this.remember?1:0
                this.$router.push(`/login/referral_code?token=${result.token}&isAutoLogin=1&isRemember=${isRemember}`)
                setTimeout(()=>{
                    this.$message.error(this.$t('userOutOfTrail'));
                },300)
            }else if (result.need_code_login||needBind) {
                this.verifyWithToken(result);
            }else if (result.need_pwd_login) {
                this.$message.error(this.$t('login_need_password'));
                this.loginType=3;
            }else{
                this.loginWithToken(result.token);
            }
        },
        loginWithToken(token){
            this.logging=true
            service.loginByToken({
                token:token,
                deviceInfo:{
                    device_id:this.deviceInfo.device_id
                }
            }).then((res)=>{
                if (res.data.error_code===0) {
                    const user=res.data.data;
                    user.fromLogin=true
                    // parseImageListToLocal([user],'avatar')
                    // this.setDefaultImg([user])
                    handleAfterLogin(user,this.remember);
                    this.$router.replace("/main/dashboard");
                }else{
                }
                setTimeout(()=>{
                    this.logging=false
                    this.isAutoLogin = false
                },1000)
            })
        },
        verifyWithToken(loginRes){
            this.token=loginRes.token;
            this.cellphone=loginRes.cellphone;
            this.email=loginRes.email;
            this.$nextTick(()=>{
                this.isShowVerify=true;
                this.$refs.twoFactorAuthentication.init();
            })
        },
        loginByVerify(verifyCode,accountType){
            this.logging=true
            service.loginByToken({
                token:this.token,
                accountType:accountType,
                code:verifyCode,
                deviceInfo:{
                    device_id:this.deviceInfo.device_id
                }
            }).then((res)=> {
                this.logging=false
                if (res.data.error_code===0) {
                    const user=res.data.data;
                    user.fromLogin=true
                    // parseImageListToLocal([user],'avatar')
                    // this.setDefaultImg([user])
                    handleAfterLogin(user,this.remember);
                    this.$router.replace("/main/dashboard");
                }else{
                }
            })
        },
        showForgetDialog(){
            this.isShowForgetPassword=true;
            // this.$root.eventBus.$emit('openForgetPasswordDialog');
        },
        loginByBinding(verifyCode,accountType,account,countryCode){
            this.logging=true
            service.loginAndBindAccount({
                account:account,
                token:this.token,
                accountType:accountType,
                code:verifyCode,
                countryCode:countryCode
            }).then((res)=> {
                this.logging=false
                if (res.data.error_code===0) {
                    const user=res.data.data;
                    user.fromLogin=true
                    parseImageListToLocal([user],'avatar')
                    this.setDefaultImg([user])
                    handleAfterLogin(user,this.remember);
                    this.$router.replace("/main/dashboard");
                }else{
                }
            })
        },
        changeLoginType(loginType){
            this.loginType=loginType;
            this.isShowForgetPassword=false;
        },
        async getVerifyCode(accountType, afsCode){
            this.requesting = true
            let verifyType='getVerityCodeToMobile';
            let checkParams={};
            let params={};
            if (accountType==='mobile') {
                verifyType='getVerityCodeToMobile';
                checkParams={
                    account:this.mobile_number,
                    accountType:'mobile',
                    countryCode:this.international_code,
                }
                params={
                    mobile:this.mobile_number,
                    afsCode:afsCode,
                    countryCode:this.international_code,
                }
            }else{
                verifyType='getVerityCodeToEmail';
                checkParams={
                    account:this.email,
                    accountType:'email'
                }
                params={
                    email:this.email,
                    afsCode:afsCode,
                }
            }
            const checkResult=await service.checkAccount(checkParams);
            if (checkResult.data.error_code===0) {
                if (checkResult.data.data.existStatus) {
                    this.submitType=1;
                    params.type='login'
                }else{
                    this.submitType=2;
                    params.type='register'
                }
            }
            service[verifyType](params).then((res)=>{
                this.requesting = false;
                if (res.data.error_code===0) {
                    this.display_count_down();
                }
            })
        },
        display_count_down(){
            if (!this.countTimer) {
                this.counter=89;
                this.countTimer = setInterval(()=>{
                    this.counter--;
                    if (this.counter == 0){
                        this.counter = 0;
                        clearInterval(this.countTimer);
                        this.countTimer = null;
                    }
                }, 1000);
            }
        },
        loginByCode(){
            if(!this.isAgreePrivacyPolicy){
                this.$message.error(this.$t('please_agree_privacy_policy'));
                return
            }
            let params={
                code:this.verifyCode,
                language:getLanguage(),
                clientType:this.systemConfig.clientType,
                deviceId:this.deviceInfo.device_id
            }
            if (this.loginType===1) {
                const mobileValidate=this.$refs.mobile_international.validate()
                if (!mobileValidate.pass){
                    this.$message.error(mobileValidate.tip)
                    return;
                }
                if (this.verifyCode.length===0){
                    const tip='login_sms_verification_code_empty';
                    this.$message.error(this.$t(tip));
                    return ;
                }
                params.account=this.mobile_number;
                params.countryCode=this.international_code;
                params.accountType='mobile';
            }else if (this.loginType===2){
                if (!Tool.isEmail(this.email)) {
                    this.$message.error(this.$t('email_is_invalid_input_again'));
                    return;
                }
                if (this.verifyCode.length===0){
                    const tip='email_verification_code_empty';
                    this.$message.error(this.$t(tip));
                    return ;
                }
                params.account=this.email;
                params.accountType='email';
            }
            this.logging=true;
            service.getLoginTokenByCode(params).then((res)=>{
                if (res.data.error_code===0) {
                    const result=res.data.data;
                    // result.need_code_login=true;
                    // result.cellphone='';
                    // result.email='';
                    this.twoFactorLogin(result);
                }else{
                    // Toast(this.$t(res.data.key)||this.$t('unknown_error'))
                }
                this.logging=false
            }).catch((e)=>{
                this.$message.error(e);
                this.logging=false
            })
        },
        register(){
            let registerParams={}
            if (this.loginType==1) {
                const mobileValidate=this.$refs.mobile_international.validate()
                if (!mobileValidate.pass){
                    this.$message.error(mobileValidate.tip)
                    return;
                }
                registerParams.accountType='mobile';
                registerParams.countryCode=this.international_code;
                registerParams.account=this.mobile_number;
                registerParams.clientType=this.systemConfig.clientType;
                if (this.verifyCode.length===0){
                    const tip='login_sms_verification_code_empty';
                    this.$message.error(this.$t(tip));
                    return ;
                }
                registerParams.code=this.verifyCode;
            }else if (this.loginType==2) {
                registerParams.accountType='email';
                registerParams.account=this.email;
                registerParams.clientType=this.systemConfig.clientType;
                if (this.verifyCode.length===0){
                    const tip='email_verification_code_empty';
                    this.$message.error(this.$t(tip));
                    return ;
                }
                registerParams.code=this.verifyCode;
            }
            this.registerV2(registerParams);
        },
        registerV2(params){
            this.logging = true;
            service.registerV2(params).then((res)=> {
                this.logging = false;
                if (res.data.error_code===0) {
                    this.logging = true;
                    if (this.loginType==1||this.loginType==2) {
                        window.localStorage.setItem('isShowSetOrganization',1);
                        this.loginWithToken(res.data.data.token)
                        return ;
                    }
                }else{
                }
            })
        },
        validateLoginForm(){
            let result=true;
            if(this.login_form.name.length === 0){
                result=false;
                this.$message.error(this.$t('login_account_empty'))
            } else if(this.login_form.pwd.length===0){
                result=false;
                this.$message.error(this.$t('login_password_empty'));
            } else if(!this.isAgreePrivacyPolicy){
                result=false;
                this.$message.error(this.$t('please_agree_privacy_policy'));
            }
            return result;
        },
        async handleValidateCaptcha(loginType){
            console.error('handleValidateCaptcha',loginType)
            try {
                if(this.isValidating){
                    return
                }
                this.isValidating = true
                const afsCode = await this.$refs['validateCaptcha'].validateCaptcha()
                console.error('handleValidateCaptcha',afsCode)
                // if(this.loginType === 3){
                //     this.loginOrRegister(afsCode)
                // }else if(this.loginType === 1){
                //     this.getVerifyCode('mobile',afsCode)
                // }else if(this.loginType===2){
                //     this.getVerifyCode('email',afsCode)
                // }
                this.isValidating = false
            } catch (error) {
                console.error('handleValidateCaptcha',error)
                this.isValidating = false
            }
        },
        loginByTracelessValid:Tool.debounce(function () {
            if(this.loginType === 3){
                if (!this.validateLoginForm()) {
                    return ;
                }
                if(this.isInternalNetworkEnv){ //内网不需要阿里云校验
                    this.loginOrRegister()
                    return
                }
            }
            if(this.loginType === 1){
                const mobileValidate=this.$refs.mobile_international.validate()
                if (!mobileValidate.pass){
                    this.$message.error(mobileValidate.tip);
                    return;
                }
                if(!this.isAgreePrivacyPolicy){
                    this.$message.error(this.$t('please_agree_privacy_policy'));
                    return;
                }
            }
            if(this.loginType===2){
                if (!Tool.isEmail(this.email)) {
                    this.$message.error(this.$t('email_is_invalid_input_again'));
                    return;
                }
                if(!this.isAgreePrivacyPolicy){
                    this.$message.error(this.$t('please_agree_privacy_policy'));
                    return;
                }
            }
            this.handleValidateCaptcha(this.loginType)
        }, 500,true),
        autoLogin(){
            if (this.logging) {
                //其他途径在尝试登录时，不自动登录
                return ;
            }
            console.log('[event] loginform autoLogin');
            const loginToken=window.localStorage.getItem('loginToken')||''
            const account=window.localStorage.getItem('account')||''
            const password=window.localStorage.getItem('password')||''
            let local_store_device_token = window.localStorage.getItem('local_store_device_token');
            const device_id = this.device_id;
            var token = "";
            if(local_store_device_token){
                var info = JSON.parse(local_store_device_token);
                if(info && info.device_id){
                    if(device_id != info.device_id){//设备id有修改,不能用原来的device_id和token登陆
                        window.localStorage.removeItem('local_store_device_token');
                    }else{
                        if(info && info.token){
                            token = info.token
                        }
                    }
                }
            }
            var that=this;
            if (loginToken!=='') {
                this.autoLoginAction({
                    action:'loginByToken',
                    loginToken:loginToken,
                })
            }else if(account!==''&&password!==''){
                this.autoLoginAction({
                    action:'getLoginToken',
                    account:account,
                    password:password,
                })
            }
        },
        autoLoginAction(params){
            this.isAutoLogin=true;
            this.changeLoginType(3);
            this.remember=true;
            this.logging=true;
            this.isAgreePrivacyPolicy = true
            if (params.action==='loginByToken') {
                this.loginWithToken(params.loginToken)
            } else if (params.action==='getLoginToken') {
                //兼容旧版本的本地密码登录，后替换为Token
                const tokenParams={
                    login_name:params.account,
                    password:params.password,
                    language:getLanguage(),
                    clientType:this.systemConfig.clientType,
                    deviceId:this.deviceInfo.device_id
                }
                service.getLoginToken(tokenParams).then((res)=>{
                    if (res.data.error_code===0) {
                        this.loginWithToken(res.data.data.token);
                    }else{
                    }
                })
            }
        },
        backToLogin(){
            this.isShowForgetPassword = false
            this.isShowRegister = false
        },
        toProtocol(){
            goPrivacyPolicy()
        },
        // 切换密码显示状态
        togglePasswordVisibility(){
            this.isShowPassword = !this.isShowPassword;
        }
    }
}
</script>
<style lang="scss">
.login_form_container{
    position: relative;
    flex: 1;
    .logo_box{
        .iconfanhui{
            position: absolute;
            top: 40px;
            left: 20px;
            cursor: pointer;
            z-index:1;
        }
        padding-top: 68px;
        &>img{
            width: 68px;
            height: 42px;
            margin: 0 auto;
            display: block;
        }
        &>p{
            font-size: 18px;
            line-height: 1;
            margin-top: 20px;
            margin-bottom: 26px;
            text-align: center;
        }
    }
    .login_qrcode{
        position: absolute;
        top: 8px;
        left: 8px;
        width: 44px;
        height: 44px;
        cursor: pointer;
    }
    .account_login{
        padding: 0px 50px 0;
    }
    .mobile_login,.email_login{
        padding: 0px 50px 0;
        .register_tip{
            font-size: 12px;
            line-height: 1;
            margin: 4px 0 10px;
            color: #999;
        }
    }
    .setting{
        margin-top: 8px;
        &>img{
            width: 20px;
            height: 20px;
            margin-right: 8px;
            cursor: pointer;
        }
        .go_to_btn{
            font-size: 12px;
            color: #b3b3b3;
            cursor: pointer;
        }
    }
    .login-form-checkbox{
        display: flex;
        flex-direction: column;
        position: absolute;
        bottom: 10px;
        left: 50px;
        .remember{

        }
        .agree_privacy_protocol{
            .el-checkbox__label{
                padding-left:7px;
                &:hover{
                    color: #6a8f8d;
                }
            }
        }
        .privacy-protocol-link{
            color: #00c59d !important;
            cursor: pointer;
            text-decoration: none;
            &:hover{
                color: #03ddb1 !important;
            }
        }
    }

    .verify_item{
        position: relative;
        .code_btn{
            position: absolute;
            right: 6px;
            top: 0px;
            bottom: 0;
            color: #51c1a5;
            font-size: 12px;
            cursor: pointer;
            line-height: 34px;
            min-width: 30px;
        }
        .ban{
            color: #999;
            pointer-events: none;
            cursor: not-allowed;
        }
    }
    // .mobile_login,.email_login{

    //     .verify_item{
    //         position:relative;
    //         margin:10px 0;
    //         .svg_wrap{
    //             position:absolute;
    //             right:1px;
    //             top:0;
    //             bottom:0;
    //             cursor: pointer;
    //             & > svg{
    //                 width:112px;
    //                 height:50px;
    //             }
    //         }
    //         .reget_btn{
    //             position: absolute;
    //             right: 0;
    //             top: 0;
    //             border-radius: 0;
    //             height: 50px;
    //             line-height: 50px;
    //             padding: 0 10px;
    //         }
    //         .get_code_btn{
    //             position:absolute;
    //             right: 0;
    //             top: 0;
    //             height: 50px;
    //             line-height: 50px;
    //             width: 50%;
    //             border-radius: 0;
    //         }
    //     }
    //     .verify_type_wrap{
    //         display:flex;
    //         align-items: center;
    //         color:#333;
    //         font-size:1rem;
    //         padding:0.7rem;
    //         border: 1px solid #aaa;
    //         border-radius: 0.5rem;
    //         margin:0.7rem 0;
    //         cursor:pointer;
    //         i{
    //             font-size: 1rem;
    //             margin-right: 6px;
    //         }
    //     }
    // }
    .password-input-wrapper {
        position: relative;
        .password-toggle-icon-fixed {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            font-size: 18px;
            z-index: 3;
            user-select: none;
            color: #666;
            &:hover {
                color: #51c1a5;
                opacity: 0.8;
            }
        }
    }
    .password-toggle-icon{
        cursor: pointer;
        color: #c0c4cc;
        font-size: 16px;
        &:hover{
            color: #51c1a5;
        }
    }
}
</style>
